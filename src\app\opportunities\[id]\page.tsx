import { Suspense } from "react";
import OpportunityDetailClient from "./OpportunityDetailClient"

// Generate static params for static export
export async function generateStaticParams() {
  // Return a list of possible IDs for static generation
  // In a real app, you would fetch this from your API or database
  return [
    { id: '1' },
    { id: '2' },
    { id: '3' },
    { id: '4' },
    { id: '5' },
  ];
}

function OpportunityContent() {
  return <OpportunityDetailClient />;
}

export default function OpportunityDetailPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading opportunity details...</p>
        </div>
      </div>
    }>
      <OpportunityContent />
    </Suspense>
  );
}

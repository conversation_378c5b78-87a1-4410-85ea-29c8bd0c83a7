'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth/auth-context';
import { useQuery } from '@apollo/client';
import { GET_USERS_QUERY } from '@/graphql/remix-queries';
import { useSearchParams } from 'next/navigation';
import { useState, useEffect, useMemo } from 'react';

export interface UserProfile {
  email: string;
  name?: string;
  artistName?: string;
  isOnboarded: boolean;
  profileImage?: string;
  [key: string]: unknown;
}

export interface RemixFlowState {
  isLoading: boolean;
  shouldShowLoading: boolean;
  shouldRedirectToOnboarding: boolean;
  shouldOpenUploadModal: boolean;
  userProfile: UserProfile | null;
  error: string | null;
}

export function useRemixFlow() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, user } = useAuth();
  const [flowState, setFlowState] = useState<RemixFlowState>({
    isLoading: true,
    shouldShowLoading: false,
    shouldRedirectToOnboarding: false,
    shouldOpenUploadModal: false,
    userProfile: null,
    error: null,
  });

  // Query user data from GraphQL API when authenticated
  const { data: userData, loading: userLoading, error: userError, refetch } = useQuery(GET_USERS_QUERY, {
    variables: { email: user?.email || '' },
    skip: !isAuthenticated || !user?.email,
    errorPolicy: 'all',
    // Add fetchPolicy to ensure we get fresh data
    fetchPolicy: 'cache-and-network'
  });

  // Get the first user from the query results
  const userProfile = userData?.users?.[0] as UserProfile | undefined;

  // Check if user needs onboarding - with better logic
  const needsOnboarding = useMemo(() => {
    if (!isAuthenticated) return false;
    if (userLoading) return false; // Don't redirect while loading
    if (userError) return true; // Treat as new user if error
    if (!userProfile) return true; // No profile found
    return !userProfile.isOnboarded; // Check onboarding status
  }, [isAuthenticated, userLoading, userError, userProfile]);

  // Check for createApplication query parameter
  const shouldCreateApplication = searchParams.get('createApplication') === 'true';

  // Refetch user data when returning from onboarding or when needed
  useEffect(() => {
    if (isAuthenticated && user?.email && !userLoading) {
      const currentPath = window.location.pathname;
      
      // If we're on the remix page, check if we need to refetch
      if (currentPath === '/attention') {
        // Check if we just completed onboarding by looking at localStorage
        // Use the same key as syncOnboardingStatus function
        const onboardingCompleted = localStorage.getItem('remix-onboarding') === 'done';
        
        if (onboardingCompleted) {
          // Clear the flag to prevent future refetches
          localStorage.removeItem('remix-onboarding');
          // Add a small delay to ensure GraphQL cache has time to update
          setTimeout(() => {
            refetch();
          }, 100);
        } else if (userProfile && needsOnboarding) {
          // User profile exists but needs onboarding, try to refetch
          refetch();
        }
      }
    }
  }, [isAuthenticated, user?.email, userLoading, userProfile, needsOnboarding, refetch]);

  useEffect(() => {
    // Determine the flow state based on current conditions
    let newState: RemixFlowState = {
      isLoading: true,
      shouldShowLoading: false,
      shouldRedirectToOnboarding: false,
      shouldOpenUploadModal: false,
      userProfile: null,
      error: null,
    };

    // If not authenticated, no special handling needed
    if (!isAuthenticated) {
      newState = {
        isLoading: false,
        shouldShowLoading: false,
        shouldRedirectToOnboarding: false,
        shouldOpenUploadModal: false,
        userProfile: null,
        error: null,
      };
    } else {
      // User is authenticated
      if (userLoading) {
        // Still loading user data
        newState = {
          isLoading: true,
          shouldShowLoading: true,
          shouldRedirectToOnboarding: false,
          shouldOpenUploadModal: false,
          userProfile: null,
          error: null,
        };
      } else if (userError) {
        // GraphQL error - treat as new user
        newState = {
          isLoading: false,
          shouldShowLoading: false,
          shouldRedirectToOnboarding: shouldCreateApplication,
          shouldOpenUploadModal: false,
          userProfile: null,
          error: userError.message,
        };
      } else if (needsOnboarding) {
        // User needs onboarding
        newState = {
          isLoading: false,
          shouldShowLoading: false,
          shouldRedirectToOnboarding: true,
          shouldOpenUploadModal: false,
          userProfile: userProfile || null,
          error: null,
        };
      } else {
        // User is onboarded
        if (shouldCreateApplication) {
          // Should open upload modal
          newState = {
            isLoading: false,
            shouldShowLoading: false,
            shouldRedirectToOnboarding: false,
            shouldOpenUploadModal: true,
            userProfile: userProfile || null,
            error: null,
          };
        } else {
          // Normal state
          newState = {
            isLoading: false,
            shouldShowLoading: false,
            shouldRedirectToOnboarding: false,
            shouldOpenUploadModal: false,
            userProfile: userProfile || null,
            error: null,
          };
        }
      }
    }

    setFlowState(newState);
  }, [isAuthenticated, userLoading, userError, userProfile, needsOnboarding, shouldCreateApplication]);

  // Handle redirects and actions
  useEffect(() => {
    if (flowState.shouldRedirectToOnboarding) {
      const queryParam = shouldCreateApplication ? '?createApplication=true' : '';
      router.push(`/attention/onboarding${queryParam}`);
    }
  }, [flowState.shouldRedirectToOnboarding, shouldCreateApplication, router]);

  return {
    ...flowState,
    shouldCreateApplication,
    needsOnboarding,
    userLoading,
    userError,
  };
} 
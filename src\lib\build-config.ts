/**
 * Build Configuration System
 * Handles different build modes and environment configurations for dual deployment
 */

export type BuildMode = 'main' | 'remix';

export interface CognitoConfig {
  region: string;
  userPoolId: string;
  clientId: string;
  identityPoolId?: string;
  domain: string;
  redirectUri: string;
}

export interface BuildConfig {
  mode: BuildMode;
  cognito: CognitoConfig;
  features: {
    remixEnabled: boolean;
    mainAppEnabled: boolean;
  };
  routing: {
    defaultAuthenticatedRoute: string;
    defaultUnauthenticatedRoute: string;
  };
}

// Direct environment variable access for better reliability

/**
 * Get the current build mode from environment variables
 */
export function getBuildMode(): BuildMode {
  const mode = process.env.NEXT_PUBLIC_BUILD_MODE;
  return mode === 'remix' ? 'remix' : 'main';
}

/**
 * Get Cognito configuration based on build mode
 */
export function getCognitoConfig(mode: BuildMode = getBuildMode()): CognitoConfig {
  if (mode === 'remix') {
    // Use remix-specific environment variables
    return {
      region: process.env.NEXT_PUBLIC_REMIX_COGNITO_REGION || process.env.NEXT_PUBLIC_COGNITO_REGION || 'us-east-2',
      userPoolId: process.env.NEXT_PUBLIC_REMIX_COGNITO_USER_POOL_ID || process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || '',
      clientId: process.env.NEXT_PUBLIC_REMIX_COGNITO_CLIENT_ID || process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '',
      identityPoolId: process.env.NEXT_PUBLIC_REMIX_COGNITO_IDENTITY_POOL_ID || process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
      domain: process.env.NEXT_PUBLIC_REMIX_COGNITO_DOMAIN || process.env.NEXT_PUBLIC_COGNITO_DOMAIN || '',
      redirectUri: process.env.NEXT_PUBLIC_REMIX_COGNITO_REDIRECT_URI || process.env.NEXT_PUBLIC_COGNITO_REDIRECT_URI || '',
    };
  } else {
    // Use main app environment variables
    return {
      region: process.env.NEXT_PUBLIC_COGNITO_REGION || 'us-east-2',
      userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || '',
      clientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '',
      identityPoolId: process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
      domain: process.env.NEXT_PUBLIC_COGNITO_DOMAIN || '',
      redirectUri: process.env.NEXT_PUBLIC_COGNITO_REDIRECT_URI || '',
    };
  }
}

/**
 * Get complete build configuration
 */
export function getBuildConfig(): BuildConfig {
  const mode = getBuildMode();
  const cognito = getCognitoConfig(mode);
  
  return {
    mode,
    cognito,
    features: {
      remixEnabled: mode === 'remix',
      mainAppEnabled: mode === 'main',
    },
    routing: {
      defaultAuthenticatedRoute: mode === 'remix' ? '/attention' : '/discover',
      defaultUnauthenticatedRoute: mode === 'remix' ? '/attention' : '/login',
    },
  };
}

/**
 * Check if current build supports remix features
 */
export function isRemixEnabled(): boolean {
  return getBuildConfig().features.remixEnabled;
}

/**
 * Check if current build supports main app features
 */
export function isMainAppEnabled(): boolean {
  return getBuildConfig().features.mainAppEnabled;
}

/**
 * Get the appropriate redirect route for authenticated users
 */
export function getDefaultAuthenticatedRoute(): string {
  return getBuildConfig().routing.defaultAuthenticatedRoute;
}

/**
 * Get the appropriate redirect route for unauthenticated users
 */
export function getDefaultUnauthenticatedRoute(): string {
  return getBuildConfig().routing.defaultUnauthenticatedRoute;
}

/**
 * Validate build configuration
 */
export function validateBuildConfig(): { isValid: boolean; errors: string[] } {
  const config = getBuildConfig();
  const errors: string[] = [];
  
  // Validate Cognito configuration
  if (!config.cognito.region) {
    errors.push('Cognito region is required');
  }
  if (!config.cognito.userPoolId) {
    errors.push('Cognito user pool ID is required');
  }
  if (!config.cognito.clientId) {
    errors.push('Cognito client ID is required');
  }
  
  // Validate build mode
  if (!['main', 'remix'].includes(config.mode)) {
    errors.push('Invalid build mode. Must be "main" or "remix"');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

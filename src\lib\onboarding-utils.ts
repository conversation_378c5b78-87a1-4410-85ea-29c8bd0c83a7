/**
 * Essential Onboarding Utilities
 * Simplified for query parameter approach - keeping only essential functions
 */

import { getBuildConfig } from './build-config';

export interface UserProfile {
  email: string;
  name?: string;
  artistName?: string;
  isOnboarded: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

/**
 * Synchronize onboarding status between GraphQL and localStorage
 * Call this after successful onboarding completion
 */
export function syncOnboardingStatus(isOnboarded: boolean): void {
  const config = getBuildConfig();

  try {
    const storageKey = config.mode === 'remix' ? 'remix-onboarding' : 'onboarding';
    const storageValue = isOnboarded ? 'done' : null;

    if (storageValue) {
      localStorage.setItem(storageKey, storageValue);
    } else {
      localStorage.removeItem(storageKey);
    }
  } catch (error) {
    console.warn('Failed to sync onboarding status to localStorage:', error);
  }
}

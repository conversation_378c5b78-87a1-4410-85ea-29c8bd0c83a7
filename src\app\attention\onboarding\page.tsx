'use client';

import React, { useEffect, useState } from 'react';
import { RemixOnboardingProvider } from '@/features/remix-onboarding/remix-onboarding-context';
import { RemixOnboardingStepper } from '@/features/remix-onboarding/remix-onboarding-stepper';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth/auth-context';
import { useQuery } from '@apollo/client';
import { GET_USERS_QUERY } from '@/graphql/remix-queries';

const steps = [
  dynamic(() => import('@/features/remix-onboarding/steps/step-1')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-2')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-3')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-4')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-5')),
];

import { useRemixOnboarding } from '@/features/remix-onboarding/remix-onboarding-context';

function RemixOnboardingSteps() {
  const { step } = useRemixOnboarding();
  const StepComponent = steps[step - 1];

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-start md:justify-center px-4 py-10">
      <div className="w-full max-w-md mx-auto">
        {/* Progress Stepper */}
        <div className="mb-8">
          <RemixOnboardingStepper />
        </div>
        
        {/* Step Content */}
        <div className="bg-background">
          <StepComponent />
        </div>
      </div>
    </div>
  );
}

export default function RemixOnboardingPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const { isAuthenticated, user } = useAuth();

  // Query user data from GraphQL API when authenticated
  const { data: userData, loading: userLoading, error: userError } = useQuery(GET_USERS_QUERY, {
    variables: { email: user?.email || '' },
    skip: !isAuthenticated || !user?.email, // Skip query if not authenticated or no email
    errorPolicy: 'all'
  });

  // Get the first user from the query results
  const userProfile = userData?.users?.[0];

  useEffect(() => {
    // Check if user is already onboarded using GraphQL data
    if (isAuthenticated && userProfile && userProfile.isOnboarded) {
      router.push('/attention');
      return;
    }

    // Debug logging and error handling
    console.log('Remix Onboarding - Auth Status:', isAuthenticated, 'User:', user?.email, 'Profile:', userProfile);

    if (userError) {
      console.error('GraphQL error in remix onboarding:', userError);
      // Continue with onboarding flow even if GraphQL fails
      // User will be treated as new user
    }

    // If not authenticated, show onboarding
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    // If user data is loaded and user is not onboarded, show onboarding
    if (!userLoading && userProfile && !userProfile.isOnboarded) {
      setIsLoading(false);
      return;
    }

    // If still loading user data, keep loading
    if (userLoading) {
      return;
    }

    // If no user profile found, show onboarding
    setIsLoading(false);
  }, [router, isAuthenticated, user, userProfile, userLoading, userError]);

  // Show loading while checking onboarding status
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <h2 className="text-xl font-semibold text-foreground">Loading...</h2>
          <p className="text-muted-foreground">Checking your onboarding status</p>
        </div>
      </div>
    );
  }

  return (
    <RemixOnboardingProvider>
      <RemixOnboardingSteps />
    </RemixOnboardingProvider>
  );
}

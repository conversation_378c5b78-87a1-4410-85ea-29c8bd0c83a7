# Smash Music - Dual Deployment Guide

Simple guide for building and deploying the Smash Music application with two separate flows.

## Build Modes

**Main Application** (`NEXT_PUBLIC_BUILD_MODE=main`)
- Full app with all features
- Routes: `/discover`, `/opportunities`, `/album`, etc.
- Blocks all `/attention/*` routes

**Remix Contest** (`NEXT_PUBLIC_BUILD_MODE=remix`)
- Only remix functionality
- Routes: `/attention`, `/attention/onboarding`
- Blocks all main app routes

## Environment Setup

Edit `.env.local` and change the build mode:

```env
# Switch between modes
NEXT_PUBLIC_BUILD_MODE=main     # or 'remix'

# Main App Cognito
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-2_2S3tMXiZJ
NEXT_PUBLIC_COGNITO_CLIENT_ID=ct6t0ujsf4qub5bb469s5cl00

# Remix Contest Cognito
NEXT_PUBLIC_REMIX_COGNITO_USER_POOL_ID=us-east-2_u0Afn8S4c
NEXT_PUBLIC_REMIX_COGNITO_CLIENT_ID=5n8eevkf3afglpmi72c4c88i9i
```

## Development

```bash
# 1. Set build mode in .env.local
# 2. Start dev server
pnpm dev
```

## Production Build

```bash
# 1. Set build mode in .env.local
# 2. Build
pnpm build
# 3. Output goes to out/ directory
```

## Deployment

**Main Application:**
```bash
# Set NEXT_PUBLIC_BUILD_MODE=main in .env.local
pnpm build
aws s3 sync out/ s3://smash-music-main-app --delete
```

**Remix Contest:**
```bash
# Set NEXT_PUBLIC_BUILD_MODE=remix in .env.local
pnpm build
aws s3 sync out/ s3://smash-music-remix-contest --delete
```

## Route Behavior

| Mode | Accessible Routes | Blocked Routes |
|------|------------------|----------------|
| Main | All main app routes | `/attention/*` |
| Remix | `/attention`, `/attention/onboarding` | All main app routes |

## Troubleshooting

- **Wrong routes showing?** Check `NEXT_PUBLIC_BUILD_MODE` in `.env.local`
- **Build issues?** Clear cache: `rm -rf .next out`
- **Auth not working?** Verify Cognito credentials for current mode

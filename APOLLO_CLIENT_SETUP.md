# Apollo Client Dual-Flow Setup

This document describes the implementation of Apollo Client for the dual-flow architecture supporting both main and remix flows.

## Overview

The application now supports two separate Apollo Client configurations:
- **Main Flow**: Uses `https://iptd5paa92.execute-api.us-east-2.amazonaws.com/beta/graphql`
- **Remix Flow**: Uses `https://lt7t8xl8ua.execute-api.us-east-2.amazonaws.com/prod/graphql`

Both flows include proper JWT authentication headers from AWS Cognito.

## Implementation Details

### 1. Apollo Client Configuration (`src/lib/apollo-client-config.ts`)

This file contains the core Apollo Client configuration system:

- **`createMainApolloClient()`**: Creates Apollo Client for main application flow
- **`createRemixApolloClient()`**: Creates Apollo Client for remix flow
- **`createApolloClient()`**: Factory function that returns the appropriate client based on `NEXT_PUBLIC_BUILD_MODE`
- **Authentication Link**: Automatically adds JWT tokens from AWS Cognito to all GraphQL requests

### 2. Authentication Token Retrieval (`src/lib/auth-utils.ts`)

Added `getAuthToken()` function that:
- Retrieves JWT tokens from AWS Amplify session
- Returns `null` if user is not authenticated
- Handles errors gracefully

### 3. Updated Apollo Client (`src/lib/apolloClient.ts`)

Simplified to use the new configuration system:
- Automatically selects correct endpoint based on build mode
- Includes authentication headers
- Maintains existing cache policies

### 4. Updated Providers (`src/app/providers.tsx`)

Modified to:
- Create Apollo Client dynamically based on build mode
- Use `useMemo` to ensure client is only created once
- Support both main and remix flows seamlessly

## Build Mode Configuration

The Apollo Client automatically adapts based on the `NEXT_PUBLIC_BUILD_MODE` environment variable:

```env
# For Main Application
NEXT_PUBLIC_BUILD_MODE=main

# For Remix Contest
NEXT_PUBLIC_BUILD_MODE=remix
```

## GraphQL Endpoints

### Main Flow
- **Endpoint**: `https://iptd5paa92.execute-api.us-east-2.amazonaws.com/beta/graphql`
- **Authentication**: JWT tokens from main Cognito user pool
- **User Pool**: `us-east-2_2S3tMXiZJ`

### Remix Flow
- **Endpoint**: `https://lt7t8xl8ua.execute-api.us-east-2.amazonaws.com/prod/graphql`
- **Authentication**: JWT tokens from remix Cognito user pool
- **User Pool**: `us-east-2_u0Afn8S4c`

## Authentication Headers

All GraphQL requests automatically include:
```
Authorization: Bearer <JWT_TOKEN>
```

The JWT token is retrieved from the current AWS Amplify session and includes:
- User identity information
- Cognito user pool claims
- Expiration and refresh logic handled by Amplify

## Error Handling

The Apollo Client is configured with:
- `errorPolicy: 'all'` for both queries and watchQueries
- Graceful handling of authentication failures
- Console logging for debugging

## Cache Configuration

Both clients use the same cache configuration with:
- Custom merge policies for `artistsConnection` queries
- Support for pagination with cursor-based connections
- Optimized for the existing GraphQL schema

## Usage

No changes required in existing components. The Apollo Client will automatically:
1. Select the correct GraphQL endpoint based on build mode
2. Add authentication headers for authenticated users
3. Handle unauthenticated requests gracefully

Example usage remains the same:
```tsx
import { useQuery } from '@apollo/client';
import { ARTISTS_QUERY } from '@/graphql/queries';

function MyComponent() {
  const { data, loading, error } = useQuery(ARTISTS_QUERY);
  // Component logic...
}
```

## Testing

To test the implementation:

1. **Switch Build Modes**: Change `NEXT_PUBLIC_BUILD_MODE` in `.env.local`
2. **Check Console**: Apollo Client logs which endpoint is being used
3. **Network Tab**: Verify correct GraphQL endpoint and Authorization headers
4. **Authentication**: Test both authenticated and unauthenticated requests

## Troubleshooting

### Common Issues

1. **Wrong Endpoint**: Check `NEXT_PUBLIC_BUILD_MODE` in `.env.local`
2. **Missing Auth Headers**: Ensure user is authenticated via AWS Cognito
3. **CORS Errors**: Verify GraphQL endpoints support the application domain
4. **Token Expiration**: Amplify handles token refresh automatically

### Debug Information

The Apollo Client logs configuration details to the console:
```
[Apollo Client] Creating client for remix mode with endpoint: https://lt7t8xl8ua.execute-api.us-east-2.amazonaws.com/prod/graphql
```

## Security Considerations

- JWT tokens are automatically managed by AWS Amplify
- Tokens are only sent to the configured GraphQL endpoints
- No sensitive information is logged in production
- Authentication state is managed securely by Cognito

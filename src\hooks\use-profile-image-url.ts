import { useEffect, useState } from 'react';
import { useLazyQuery } from '@apollo/client';
import { GET_PROFILE_PICTURE_URL } from '@/graphql/remix-queries';

export function useProfileImageUrl(profileImageKey: string | null) {
  const [viewUrl, setViewUrl] = useState<string | null>(null);
  const [getProfilePictureUrl, { data, loading, error }] = useLazyQuery(GET_PROFILE_PICTURE_URL);
  console.log("🚀 ~ useProfileImageUrl ~ data:", data)

  useEffect(() => {
    if (profileImageKey) {
      getProfilePictureUrl();
    }
  }, [profileImageKey, getProfilePictureUrl]);

  useEffect(() => {
    if (data?.getProfilePictureUrl?.viewUrl) {
      setViewUrl(data.getProfilePictureUrl.viewUrl);
    }
  }, [data]);

  return { viewUrl, loading, error };
}

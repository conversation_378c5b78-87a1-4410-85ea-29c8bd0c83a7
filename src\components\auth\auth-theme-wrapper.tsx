'use client';

import React, { useEffect } from 'react';
import { getBuildConfig } from '@/lib/build-config';
import { remixThemeConfig } from '@/app/attention/theme-config';

interface AuthThemeWrapperProps {
  children: React.ReactNode;
}

// Safari-compatible color conversion function
function hslToRgb(h: number, s: number, l: number): string {
  s /= 100;
  l /= 100;
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;
  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return `${r}, ${g}, ${b}`;
}

// Parse HSL string and convert to RGB for Safari compatibility
function parseHslToRgb(hslString: string): string {
  const match = hslString.match(/(\d+(?:\.\d+)?),?\s*(\d+(?:\.\d+)?)%?,?\s*(\d+(?:\.\d+)?)%?/);
  if (!match) return '0, 0, 0';

  const h = parseFloat(match[1]);
  const s = parseFloat(match[2]);
  const l = parseFloat(match[3]);

  return hslToRgb(h, s, l);
}

/**
 * Theme wrapper for authentication pages
 *
 * Behavior:
 * - When NEXT_PUBLIC_BUILD_MODE=remix: Applies remix theme (pink primary colors)
 * - When NEXT_PUBLIC_BUILD_MODE=main: Uses default theme (no wrapper applied)
 *
 * This ensures the login flow matches the app's current mode:
 * - Remix mode: Pink themed login/signup/forgot password pages
 * - Artist mode: Default themed login/signup/forgot password pages
 */
export function AuthThemeWrapper({ children }: AuthThemeWrapperProps) {
  // const { resolvedTheme } = useTheme();
  const config = getBuildConfig();
  const isRemixMode = config.mode === 'remix';

  // Generate Safari-compatible CSS variables for remix mode
  const cssVariables = React.useMemo(() => {
    if (!isRemixMode) return null;

    // Force light theme mode for remix flow
    const mode = 'light';
    const themeColors = remixThemeConfig[mode];

    return {
      // Primary colors - provide both HSL and RGB fallbacks for Safari
      '--primary': `rgb(${parseHslToRgb(themeColors.primary.DEFAULT)})`,
      '--primary-hsl': `hsl(${themeColors.primary.DEFAULT})`,
      '--primary-foreground': `rgb(${parseHslToRgb(themeColors.primary.foreground)})`,
      '--primary-foreground-hsl': `hsl(${themeColors.primary.foreground})`,

      // Secondary colors
      '--secondary': `rgb(${parseHslToRgb(themeColors.secondary.DEFAULT)})`,
      '--secondary-hsl': `hsl(${themeColors.secondary.DEFAULT})`,
      '--secondary-foreground': `rgb(${parseHslToRgb(themeColors.secondary.foreground)})`,
      '--secondary-foreground-hsl': `hsl(${themeColors.secondary.foreground})`,

      // Muted colors
      '--muted': `rgb(${parseHslToRgb(themeColors.muted.DEFAULT)})`,
      '--muted-hsl': `hsl(${themeColors.muted.DEFAULT})`,
      '--muted-foreground': `rgb(${parseHslToRgb(themeColors.muted.foreground)})`,
      '--muted-foreground-hsl': `hsl(${themeColors.muted.foreground})`,

      // Accent colors
      '--accent': `rgb(${parseHslToRgb(themeColors.accent.DEFAULT)})`,
      '--accent-hsl': `hsl(${themeColors.accent.DEFAULT})`,
      '--accent-foreground': `rgb(${parseHslToRgb(themeColors.accent.foreground)})`,
      '--accent-foreground-hsl': `hsl(${themeColors.accent.foreground})`,

      // Background and foreground
      '--background': `rgb(${parseHslToRgb(themeColors.background)})`,
      '--background-hsl': `hsl(${themeColors.background})`,
      '--foreground': `rgb(${parseHslToRgb(themeColors.foreground)})`,
      '--foreground-hsl': `hsl(${themeColors.foreground})`,

      // Card colors
      '--card': `rgb(${parseHslToRgb(themeColors.card.DEFAULT)})`,
      '--card-hsl': `hsl(${themeColors.card.DEFAULT})`,
      '--card-foreground': `rgb(${parseHslToRgb(themeColors.card.foreground)})`,
      '--card-foreground-hsl': `hsl(${themeColors.card.foreground})`,

      // UI elements
      '--border': `rgb(${parseHslToRgb(themeColors.border)})`,
      '--border-hsl': `hsl(${themeColors.border})`,
      '--input': `rgb(${parseHslToRgb(themeColors.input)})`,
      '--input-hsl': `hsl(${themeColors.input})`,
      '--ring': `rgb(${parseHslToRgb(themeColors.ring)})`,
      '--ring-hsl': `hsl(${themeColors.ring})`,

      // Destructive colors
      '--destructive': `rgb(${parseHslToRgb(themeColors.destructive.DEFAULT)})`,
      '--destructive-hsl': `hsl(${themeColors.destructive.DEFAULT})`,
      '--destructive-foreground': `rgb(${parseHslToRgb(themeColors.destructive.foreground)})`,
      '--destructive-foreground-hsl': `hsl(${themeColors.destructive.foreground})`,
    } as React.CSSProperties;
  }, [isRemixMode]);

  // Apply CSS variables to document root for better Safari compatibility
  useEffect(() => {
    if (!cssVariables) return;

    const root = document.documentElement;

    // Apply all CSS variables to the document root
    Object.entries(cssVariables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });

    // Cleanup function to remove variables when component unmounts
    return () => {
      Object.keys(cssVariables).forEach((key) => {
        root.style.removeProperty(key);
      });
    };
  }, [cssVariables]);

  // If in remix mode, apply remix theme
  if (isRemixMode) {
    return (
      <div
        className="auth-remix-theme-scope"
        data-theme="light"
      >
        {children}
      </div>
    );
  }

  // For artist mode, use default theme (no wrapper needed)
  return <>{children}</>;
}

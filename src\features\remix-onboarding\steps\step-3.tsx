'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useRemixOnboarding, validateStep3 } from '../remix-onboarding-context';

export default function Step3() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [bio, setBio] = useState(data.bio);
  const [location, setLocation] = useState(data.location);
  // Split phone input into country code and number
  const [countryCode, setCountryCode] = useState(data.countryCode || '+1');
  const [mobileNumber, setMobileNumber] = useState(data.mobileNumber || '');
  const [phoneError, setPhoneError] = useState('');

  // Validate country code and mobile number
  const validatePhoneNumber = (code: string, number: string): string => {
    if (!code.trim() || !number.trim()) return 'Country code and mobile number are required';
    if (!/^\+\d{1,3}$/.test(code)) return 'Country code must start with + and be 1-3 digits';
    if (!/^\d{10}$/.test(number)) return 'Mobile number must be exactly 10 digits';
    return '';
  };

  const handleCountryCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/[^\d+]/g, '');
    if (!value.startsWith('+')) value = '+' + value;
    if (value.length > 4) value = value.slice(0, 4); // + and up to 3 digits
    setCountryCode(value);
    if (phoneError) setPhoneError('');
  };

  const handleMobileNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/[^\d]/g, '');
    if (value.length > 10) value = value.slice(0, 10);
    setMobileNumber(value);
    if (phoneError) setPhoneError('');
  };

  const handlePhoneBlur = () => {
    const error = validatePhoneNumber(countryCode, mobileNumber);
    setPhoneError(error);
  };

  const handleContinue = () => {
    // Combine countryCode and mobileNumber for validation and data update
    const phoneNumber = countryCode + mobileNumber;
    const updatedData = { bio, location, countryCode, mobileNumber, phoneNumber };
    updateData(updatedData);
    if (
      validateStep3({ ...data, ...updatedData }) &&
      !validatePhoneNumber(countryCode, mobileNumber)
    ) {
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const isValid =
    bio.trim().length > 0 &&
    location.trim().length > 0 &&
    countryCode.trim().length > 0 &&
    mobileNumber.trim().length === 10 &&
    !phoneError;

  return (
    <div className="space-y-6">
      {/* Title */}
      <div className="flex flex-col justify-start items-start">
        <h1 className="max-w-[328.81px] flex justify-center text-foreground text-3xl font-bold leading-10">
          Describe yourself
        </h1>
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Bio Field */}
        <div className="space-y-2">
          <Label
            htmlFor="bio"
            className="text-muted-foreground text-sm font-normal"
          >
            Write a short bio best describing you *
          </Label>
          <Textarea
            id="bio"
            value={bio}
            onChange={(e) => {
              if (e.target.value.length <= 1000) {
                setBio(e.target.value);
              }
            }}
            placeholder="e.g. I am an enthusiastic song writer"
            rows={4}
            maxLength={1000}
            className="w-full px-4 py-3.5 bg-card border border-border rounded text-sm font-secondary text-card-foreground placeholder:font-secondary placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary resize-none"
          />
          <p className="text-xs text-muted-foreground font-secondary">
            {bio.length}/1000 characters
          </p>
        </div>

        {/* Location Field */}
        <div className="space-y-2">
          <Label
            htmlFor="location"
            className="text-muted-foreground text-sm font-normal"
          >
            What&apos;s your location? *
          </Label>
          <Input
            id="location"
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="e.g. Los Angeles"
            className="w-full px-4 py-3 bg-card border border-border rounded text-sm font-secondary text-card-foreground placeholder:font-secondary placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
          />
        </div>

        {/* Phone Number Field */}
        <div className="flex flex-col justify-start items-start gap-1.5">
          <Label
            htmlFor="phone-number"
            className="text-muted-foreground text-sm font-normal"
          >
            What&apos;s your phone number? *
          </Label>
          <div className="flex gap-2 w-full">
            <Input
              id="country-code"
              type="text"
              value={countryCode}
              onChange={handleCountryCodeChange}
              onBlur={handlePhoneBlur}
              placeholder="+1"
              className={`w-20 px-4 py-2.5 bg-card border rounded text-sm font-secondary text-card-foreground focus:ring-1 ${
                phoneError
                  ? 'border-destructive focus:border-destructive focus:ring-destructive'
                  : 'border-border focus:border-primary focus:ring-primary'
              }`}
              maxLength={4}
            />
            <Input
              id="mobile-number"
              type="text"
              value={mobileNumber}
              onChange={handleMobileNumberChange}
              onBlur={handlePhoneBlur}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && isValid) {
                  handleContinue();
                }
              }}
              placeholder="1234567890"
              className={`flex-1 px-4 py-2.5 bg-card border rounded text-sm font-secondary text-card-foreground focus:ring-1 ${
                phoneError
                  ? 'border-destructive focus:border-destructive focus:ring-destructive'
                  : 'border-border focus:border-primary focus:ring-primary'
              }`}
              maxLength={10}
            />
          </div>
          {phoneError && (
            <p className="text-xs text-destructive font-secondary">
              {phoneError}
            </p>
          )}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="cursor-pointer px-6 py-3 font-arvo font-bold text-base border-muted-foreground text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="cursor-pointer px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}

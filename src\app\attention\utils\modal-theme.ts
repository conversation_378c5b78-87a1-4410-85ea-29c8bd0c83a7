'use client';

import { remixThemeConfig } from '../theme-config';

// Safari-compatible color conversion function
function hslToRgb(h: number, s: number, l: number): string {
  s /= 100;
  l /= 100;
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;
  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return `${r}, ${g}, ${b}`;
}

// Parse HSL string and convert to RGB for Safari compatibility
function parseHslToRgb(hslString: string): string {
  const match = hslString.match(/(\d+(?:\.\d+)?),?\s*(\d+(?:\.\d+)?)%?,?\s*(\d+(?:\.\d+)?)%?/);
  if (!match) return '0, 0, 0';

  const h = parseFloat(match[1]);
  const s = parseFloat(match[2]);
  const l = parseFloat(match[3]);

  return hslToRgb(h, s, l);
}

/**
 * Utility hook to get remix theme CSS variables for modals
 * Use this in any modal component within the remix section
 *
 * @example
 * ```tsx
 * import { useRemixModalTheme } from '@/app/remix/utils/modal-theme';
 *
 * export function MyModal({ isOpen, onClose }) {
 *   const { cssVariables, themeMode } = useRemixModalTheme();
 *
 *   return (
 *     <Dialog open={isOpen} onOpenChange={onClose}>
 *       <DialogContent style={cssVariables} data-theme={themeMode}>
 *         <Button className="bg-primary text-primary-foreground">
 *           Themed Button
 *         </Button>
 *       </DialogContent>
 *     </Dialog>
 *   );
 * }
 * ```
 */
export function useRemixModalTheme() {
  // const { resolvedTheme } = useTheme();

  // const mode = resolvedTheme === 'dark' ? 'dark' : 'light';
  const mode = 'light';
  const themeColors = remixThemeConfig[mode];

  // Generate Safari-compatible CSS variables
  const cssVariables: React.CSSProperties = {
    '--font-primary': `var(--font-zilla-slab), 'Zilla Slab', serif`,
    '--font-secondary': `var(--font-inter), 'Inter', sans-serif`,
    '--font-family': `var(--font-primary)`,

    // Primary colors - provide both HSL and RGB fallbacks for Safari
    '--primary': `rgb(${parseHslToRgb(themeColors.primary.DEFAULT)})`,
    '--primary-hsl': `hsl(${themeColors.primary.DEFAULT})`,
    '--primary-foreground': `rgb(${parseHslToRgb(themeColors.primary.foreground)})`,
    '--primary-foreground-hsl': `hsl(${themeColors.primary.foreground})`,

    // Secondary colors
    '--secondary': `rgb(${parseHslToRgb(themeColors.secondary.DEFAULT)})`,
    '--secondary-hsl': `hsl(${themeColors.secondary.DEFAULT})`,
    '--secondary-foreground': `rgb(${parseHslToRgb(themeColors.secondary.foreground)})`,
    '--secondary-foreground-hsl': `hsl(${themeColors.secondary.foreground})`,

    // Muted colors
    '--muted': `rgb(${parseHslToRgb(themeColors.muted.DEFAULT)})`,
    '--muted-hsl': `hsl(${themeColors.muted.DEFAULT})`,
    '--muted-foreground': `rgb(${parseHslToRgb(themeColors.muted.foreground)})`,
    '--muted-foreground-hsl': `hsl(${themeColors.muted.foreground})`,

    // Accent colors
    '--accent': `rgb(${parseHslToRgb(themeColors.accent.DEFAULT)})`,
    '--accent-hsl': `hsl(${themeColors.accent.DEFAULT})`,
    '--accent-foreground': `rgb(${parseHslToRgb(themeColors.accent.foreground)})`,
    '--accent-foreground-hsl': `hsl(${themeColors.accent.foreground})`,

    // Background and foreground
    '--background': `rgb(${parseHslToRgb(themeColors.background)})`,
    '--background-hsl': `hsl(${themeColors.background})`,
    '--foreground': `rgb(${parseHslToRgb(themeColors.foreground)})`,
    '--foreground-hsl': `hsl(${themeColors.foreground})`,

    // Card colors
    '--card': `rgb(${parseHslToRgb(themeColors.card.DEFAULT)})`,
    '--card-hsl': `hsl(${themeColors.card.DEFAULT})`,
    '--card-foreground': `rgb(${parseHslToRgb(themeColors.card.foreground)})`,
    '--card-foreground-hsl': `hsl(${themeColors.card.foreground})`,

    // UI elements
    '--border': `rgb(${parseHslToRgb(themeColors.border)})`,
    '--border-hsl': `hsl(${themeColors.border})`,
    '--input': `rgb(${parseHslToRgb(themeColors.input)})`,
    '--input-hsl': `hsl(${themeColors.input})`,
    '--ring': `rgb(${parseHslToRgb(themeColors.ring)})`,
    '--ring-hsl': `hsl(${themeColors.ring})`,

    // Destructive colors
    '--destructive': `rgb(${parseHslToRgb(themeColors.destructive.DEFAULT)})`,
    '--destructive-hsl': `hsl(${themeColors.destructive.DEFAULT})`,
    '--destructive-foreground': `rgb(${parseHslToRgb(themeColors.destructive.foreground)})`,
    '--destructive-foreground-hsl': `hsl(${themeColors.destructive.foreground})`,
  } as React.CSSProperties;

  return {
    cssVariables,
    themeMode: mode,
    themeColors
  };
}

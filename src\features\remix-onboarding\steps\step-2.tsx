'use client';

import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useRemixOnboarding } from '../remix-onboarding-context';
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';
import { useLazyQuery } from '@apollo/client';
import { GET_PROFILE_UPLOAD_URL } from '@/graphql/remix-queries';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Upload, AlertCircle } from 'lucide-react';
import Image from 'next/image';

export default function Step2() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);


  // S3 upload hook - simplified!
  const { imageUrl, isUploading, progress, error, uploadImage } = useS3ImageUpload();
  console.log("🚀 ~ Step2 ~ imageUrl:", imageUrl)

  // Apollo GraphQL lazy query for presigned URL
  const [getPresignUrl] = useLazyQuery(GET_PROFILE_UPLOAD_URL, { fetchPolicy: 'no-cache' });

  const handleFileSelect = async (file: File) => {
    if (!file) return;

    // Check if it's a supported image type
    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!supportedTypes.includes(file.type)) {
      // Let the hook handle the validation and show the proper error message
      await uploadImage(file);
      return;
    }

    // Store file reference for form data
    updateData({ profilePhoto: file });

    // Get presigned URL from GraphQL
    let presignUrl, s3Key, viewUrl;
    try {
      const { data: presignData } = await getPresignUrl({
        variables: {
          fileName: file.name,
          contentType: file.type,
        },
      });
      presignUrl = presignData?.getProfileUploadUrl?.uploadUrl;
      s3Key = presignData?.getProfileUploadUrl?.key;
      viewUrl = presignData?.getProfileUploadUrl?.viewUrl;
    } catch (err) {
      console.error('Failed to get presigned URL', err);
      return;
    }
    
    if (!presignUrl || !s3Key) {
      console.error('Presigned URL or key missing');
      return;
    }
    
    console.log("🚀 ~ handleFileSelect ~ viewUrl:", viewUrl)

    // Upload to S3 using presigned URL and objectKey from GraphQL
    const success = await uploadImage(
      file,
      () => {
        // Always update the viewUrl after upload, so it is available for display
        updateData({
          profilePhotoS3Url: s3Key, // Save S3 key (not presignUrl)
          profilePhotoUrl: viewUrl, // Save viewUrl for display and mutation
        });
      },
      presignUrl,
      s3Key
    );

    if (success) {
      console.log('Upload successful!');
    } else {
      console.log('Upload failed');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleRetryUpload = async () => {
    if (data.profilePhoto) {
      await handleFileSelect(data.profilePhoto);
    }
  };

  const handleContinue = () => {
    // if (validateStep2(data)) {
      nextStep();
    // }
  };

  // const isValid = imageUrl || data.profilePhoto; // Valid if we have image URL or file
  const showUploadProgress = isUploading && progress > 0;

  const handleBack = () => {
    prevStep();
  };

  return (
    <div className="space-y-6">
      {/* Title */}
      <h1 className='text-foreground text-3xl font-bold leading-10'>
          Choose a profile photo
        </h1>

      {/* Upload Area */}
      <div
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleContinue();
          }
        }}
        className="space-y-5"
      >
        {imageUrl ? (
          /* Photo Uploaded State */
          <div className="px-6 py-8 bg-white rounded-xl shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] outline outline-1 outline-offset-[-0.50px] outline-[#f0f0f0] flex flex-col justify-start items-center gap-6">
            {/* Success message with upload status */}
            <div className="flex items-center gap-2">
              {!isUploading ? (
                <>
                  <CheckCircle className="w-5 h-5 text-primary" />
                  <h1 className="text-foreground text-lg font-semibold leading-normal">
                    Looking Good!
                  </h1>
                </>
              ) : (
                <>
                  <Upload className="w-5 h-5 text-primary" />
                  <h1 className="text-foreground text-lg font-semibold leading-normal">
                    {isUploading ? 'Uploading...' : 'Ready to Upload'}
                  </h1>
                </>
              )}
            </div>

            {/* Upload Progress */}
            {showUploadProgress && (
              <div className="w-full max-w-xs">
                <Progress value={progress} className="h-2" />
                <p className="text-sm text-muted-foreground text-center mt-1 font-secondary">
                  {progress}% uploaded
                </p>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="space-y-2">
                <Alert className="max-w-xs">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm font-secondary">
                    {error}
                  </AlertDescription>
                </Alert>
                <Button
                  onClick={handleRetryUpload}
                  variant="outline"
                  size="sm"
                  disabled={isUploading}
                  className="text-xs"
                >
                  Retry Upload
                </Button>
              </div>
            )}

            <div className="w-[140px] h-[140px] rounded-full overflow-hidden">
        {/* Always use profilePhotoUrl if available, fallback to imageUrl (preview) */}
        {data.profilePhotoUrl ? (
          <Image
            src={data.profilePhotoUrl}
            alt="Profile preview"
            width={140}
            height={140}
            className="w-full h-full object-cover"
            unoptimized
          />
        ) : (
          <Image
            src={imageUrl}
            alt="Profile preview"
            width={140}
            height={140}
            className="w-full h-full object-cover"
          />
        )}
            </div>
            <Button
              onClick={handleUploadClick}
              disabled={isUploading}
              className="bg-black text-white text-sm font-bold leading-[21px] px-8 py-6 rounded-full hover:bg-gray-800 disabled:opacity-50"
            >
              {isUploading ? 'Uploading...' : 'Change Photo'}
            </Button>
          </div>
        ) : (
          /* Initial Upload State */
          <>
            {/* Drag & Drop Area */}
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={`border-2 border-dashed rounded-xl p-14 text-center transition-colors ${
                isDragOver
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground bg-transparent'
              }`}
            >
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-card-foreground leading-normal">
                  Add a profile photo
                </h3>
                <p className="text-sm text-muted-foreground font-secondary">
                  Drag and drop or click to upload
                </p>

                {/* Upload Progress for initial state */}
                {showUploadProgress && (
                  <div className="max-w-xs mx-auto">
                    <Progress value={progress} className="h-2" />
                    <p className="text-sm text-muted-foreground text-center mt-1 font-secondary">
                      {progress}% uploaded
                    </p>
                  </div>
                )}

                {/* Error Message for initial state */}
                {error && (
                  <div className="space-y-2">
                    <Alert className="max-w-xs mx-auto">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm font-secondary">
                        {error}
                      </AlertDescription>
                    </Alert>
                    {data.profilePhoto && (
                      <Button
                        onClick={handleRetryUpload}
                        variant="outline"
                        size="sm"
                        disabled={isUploading}
                        className="text-xs mx-auto"
                      >
                        Retry Upload
                      </Button>
                    )}
                  </div>
                )}

                <Button
                  onClick={handleUploadClick}
                  disabled={isUploading}
                  className="bg-primary text-primary-foreground font-bold text-sm px-4 py-2.5 rounded-full hover:bg-primary/90 mt-4 disabled:opacity-50"
                >
                  {isUploading ? 'Uploading...' : 'Upload a photo'}
                </Button>
              </div>
            </div>
          </>
        )}

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".jpg,.jpeg,.png,.webp,image/jpeg,image/png,image/webp"
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="cursor-pointer  px-6 py-3 font-arvo font-bold text-base border-muted-foreground text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={isUploading}
          className="cursor-pointer  px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isUploading ? 'Uploading...' : 'Continue'}
        </Button>
      </div>
    </div>
  );
}

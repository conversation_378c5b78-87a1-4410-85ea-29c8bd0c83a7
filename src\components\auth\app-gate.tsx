// AppGate: Shows a loading screen until auth is resolved and handles route-based redirects. Only renders children when user is allowed to view the route.
"use client";
import { useAuth } from "@/contexts/auth/auth-context";
import { getAuthRedirect } from "@/lib/auth-routes";
import { Loader2 } from "lucide-react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { getBuildConfig } from "@/lib/build-config";

export default function AppGate({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const redirectPath = !isLoading ? getAuthRedirect(pathname, isAuthenticated) : null;

  useEffect(() => {
    if (redirectPath) {
      const config = getBuildConfig();

      // For remix mode, preserve createApplication query parameter
      if (config.mode === 'remix' && searchParams.get('createApplication') === 'true') {
        const redirectWithQuery = `${redirectPath}?createApplication=true`;
        console.log('AppGate: Preserving createApplication query parameter in redirect:', redirectWithQuery);
        router.replace(redirectWithQuery);
      } else {
        router.replace(redirectPath);
      }
    }
  }, [redirectPath, router, searchParams]);

  if (isLoading || redirectPath) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-background text-foreground">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait while we verify your session</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 
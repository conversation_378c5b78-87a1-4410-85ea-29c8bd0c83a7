import { ApolloClient, InMemoryCache, HttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { getBuildMode } from './build-config';
import { getAuthToken } from './auth-utils';

/**
 * GraphQL endpoint configuration for different build modes
 */
const GRAPHQL_ENDPOINTS = {
  main: 'https://iptd5paa92.execute-api.us-east-2.amazonaws.com/beta/graphql',
  remix: 'https://lt7t8xl8ua.execute-api.us-east-2.amazonaws.com/prod/graphql',
} as const;

/**
 * Create HTTP link with appropriate endpoint based on build mode
 */
// function createHttpLink() {
//   const buildMode = getBuildMode();
//   const uri = GRAPHQL_ENDPOINTS[buildMode];
  
//   return new HttpLink({
//     uri,
//   });
// }

/**
 * Create authentication link that adds <PERSON>W<PERSON> token to requests
 */
function createAuthLink() {
  return setContext(async (_, { headers }) => {
    try {
      const token = await getAuthToken(); //fix this later
      
      return {
        headers: {
          ...headers,
          ...(token && { Authorization: `Bearer ${token}` }),
        },
      };
    } catch (error) {
      console.error('Failed to set auth headers:', error);
      return { headers };
    }
  });
}

/**
 * Create Apollo Client cache configuration
 */
function createCache() {
  return new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          artistsConnection: {
            keyArgs: ["where"],
            merge(existing, incoming, { args }) {
              // For initial load or new search, replace existing data
              if (!existing || !args?.after) {
                return incoming;
              }

              // For pagination (fetchMore), merge the edges
              return {
                ...incoming,
                edges: [...(existing.edges || []), ...(incoming.edges || [])],
              };
            },
          },
        },
      },
    },
  });
}

/**
 * Create Apollo Client for main application flow
 */
export function createMainApolloClient() {
  const httpLink = new HttpLink({
    uri: GRAPHQL_ENDPOINTS.main,
  });

  const authLink = createAuthLink();
  const link = from([authLink, httpLink]);

  return new ApolloClient({
    link,
    cache: createCache(),
    defaultOptions: {
      watchQuery: {
        errorPolicy: 'all',
      },
      query: {
        errorPolicy: 'all',
      },
    },
  });
}

/**
 * Create Apollo Client for remix flow
 */
export function createRemixApolloClient() {
  const httpLink = new HttpLink({
    uri: GRAPHQL_ENDPOINTS.remix,
  });

  const authLink = createAuthLink();
  const link = from([authLink, httpLink]);

  return new ApolloClient({
    link,
    cache: createCache(),
    defaultOptions: {
      watchQuery: {
        errorPolicy: 'all',
      },
      query: {
        errorPolicy: 'all',
      },
    },
  });
}

/**
 * Create Apollo Client based on current build mode
 */
export function createApolloClient() {
  const buildMode = getBuildMode();
  const endpoint = GRAPHQL_ENDPOINTS[buildMode];

  console.log(`[Apollo Client] Creating client for ${buildMode} mode with endpoint: ${endpoint}`);

  if (buildMode === 'remix') {
    return createRemixApolloClient();
  } else {
    return createMainApolloClient();
  }
}

/**
 * Get the GraphQL endpoint for current build mode
 */
export function getGraphQLEndpoint(): string {
  const buildMode = getBuildMode();
  return GRAPHQL_ENDPOINTS[buildMode];
}

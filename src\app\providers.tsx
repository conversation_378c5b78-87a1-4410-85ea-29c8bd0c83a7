'use client';

import { ApolloProvider } from '@apollo/client';
import { ReactNode, useMemo } from 'react';
import { createApolloClient } from '../lib/apollo-client-config';

interface ProvidersProps {
  children: ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  // Create Apollo client based on current build mode
  // Using useMemo to ensure client is only created once per build mode
  const apolloClient = useMemo(() => createApolloClient(), []);

  return <ApolloProvider client={apolloClient}>{children}</ApolloProvider>;
}

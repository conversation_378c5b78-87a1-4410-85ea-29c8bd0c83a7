'use client';

import { useRouter } from 'next/navigation';
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useMutation } from '@apollo/client';
import { CREATE_USERS_MUTATION } from '@/graphql/remix-queries';
import { useAuth } from '@/contexts/auth/auth-context';
import { toast } from 'sonner';
import { syncOnboardingStatus } from '@/lib/onboarding-utils';
import { roleGroups } from './steps/data'; // Import roleGroups here
// Using simple query parameter approach instead of complex Flow Manager

// Types for form data
export interface RemixOnboardingData {
  // Step 1: Personal Info
  realName: string;
  artistName: string;
  
  // Step 2: Profile Photo
  profilePhoto: File | null;
  profilePhotoUrl: string;
  profilePhotoS3Url: string; // S3 URL for uploaded image
  
  // Step 3: Bio, Location and Phone
  bio: string;
  location: string;
  phoneNumber: string;
  countryCode?: string; // Added for split phone input
  mobileNumber?: string; // Added for split phone input
  
  // Step 4: Role Selection
  roles: {
    // Songwriter/Composer roles
    toplineWriter: boolean;
    lyricist: boolean;
    melodyWriter: boolean;
    beatmakerTrackmaker: boolean;
    arranger: boolean;
    remixer: boolean;
    orchestrator: boolean;
    filmTvComposer: boolean;
    jingleWriter: boolean;
    
    // Producer roles
    vocalProducer: boolean;
    mixingProducer: boolean;
    recordingProducer: boolean;
    arrangementProducer: boolean;
    synthProducer: boolean;
    soundDesigner: boolean;
    orchestralProducer: boolean;
    executiveProducer: boolean;
    beatmaker: boolean;
    remixerProducer: boolean;
    
    // Musician/Instrumentalist roles
    guitarist: boolean;
    drummer: boolean;
    keyboardist: boolean;
    stringsPlayer: boolean;
    woodwindPlayer: boolean;
    brassPlayer: boolean;
    folkInstrumentalist: boolean;
    electronicInstrumentalist: boolean;
    worldInstrumentalist: boolean;
    sessionMusician: boolean;
    multiInstrumentalist: boolean;
    orchestraMember: boolean;
    
    // Vocalist roles
    leadVocalist: boolean;
    backgroundVocalist: boolean;
    sessionVocalist: boolean;
    choirSinger: boolean;
    operaSinger: boolean;
    vocalArranger: boolean;
    beatboxer: boolean;
    rapVocalist: boolean;
    voiceoverArtist: boolean;
    vocalCoach: boolean;
    
    // Engineer/Editor roles
    recordingEngineer: boolean;
    mixingEngineer: boolean;
    masteringEngineer: boolean;
    audioEditor: boolean;
    soundDesignerEngineer: boolean;
    vocalEditor: boolean;
    liveSoundEngineer: boolean;
    studioTechnician: boolean;
    audioRestoration: boolean;
    broadcastEngineer: boolean;
    
    // Artist/Performer roles
    soloArtist: boolean;
    bandMember: boolean;
    djArtist: boolean;
    livePerformer: boolean;
    coverArtist: boolean;
    tributeArtist: boolean;
    musicalTheater: boolean;
    classicalPerformer: boolean;
    busker: boolean;
    performanceArtist: boolean;
  };
  
  // Step 5: Social Links
  socialLinks: {
    spotify: string;
    appleMusic: string;
    youtube: string;
    instagram: string;
    x: string;
    tiktok: string;
    linkedin: string;
    website: string;
  };
}

interface RemixOnboardingContextType {
  step: number;
  data: RemixOnboardingData;
  isLoading: boolean;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  updateData: (updates: Partial<RemixOnboardingData>) => void;
  submitOnboarding: () => Promise<void>;
  resetOnboarding: () => void;
}

const initialData: RemixOnboardingData = {
  realName: '',
  artistName: '',
  profilePhoto: null,
  profilePhotoUrl: '',
  profilePhotoS3Url: '',
  bio: '',
  location: '',
  phoneNumber: '',
  countryCode: undefined,
  mobileNumber: undefined,
  roles: {
    // Songwriter/Composer roles
    toplineWriter: false,
    lyricist: false,
    melodyWriter: false,
    beatmakerTrackmaker: false,
    arranger: false,
    remixer: false,
    orchestrator: false,
    filmTvComposer: false,
    jingleWriter: false,
    
    // Producer roles
    vocalProducer: false,
    mixingProducer: false,
    recordingProducer: false,
    arrangementProducer: false,
    synthProducer: false,
    soundDesigner: false,
    orchestralProducer: false,
    executiveProducer: false,
    beatmaker: false,
    remixerProducer: false,
    
    // Musician/Instrumentalist roles
    guitarist: false,
    drummer: false,
    keyboardist: false,
    stringsPlayer: false,
    woodwindPlayer: false,
    brassPlayer: false,
    folkInstrumentalist: false,
    electronicInstrumentalist: false,
    worldInstrumentalist: false,
    sessionMusician: false,
    multiInstrumentalist: false,
    orchestraMember: false,
    
    // Vocalist roles
    leadVocalist: false,
    backgroundVocalist: false,
    sessionVocalist: false,
    choirSinger: false,
    operaSinger: false,
    vocalArranger: false,
    beatboxer: false,
    rapVocalist: false,
    voiceoverArtist: false,
    vocalCoach: false,
    
    // Engineer/Editor roles
    recordingEngineer: false,
    mixingEngineer: false,
    masteringEngineer: false,
    audioEditor: false,
    soundDesignerEngineer: false,
    vocalEditor: false,
    liveSoundEngineer: false,
    studioTechnician: false,
    audioRestoration: false,
    broadcastEngineer: false,
    
    // Artist/Performer roles
    soloArtist: false,
    bandMember: false,
    djArtist: false,
    livePerformer: false,
    coverArtist: false,
    tributeArtist: false,
    musicalTheater: false,
    classicalPerformer: false,
    busker: false,
    performanceArtist: false,
  },
  socialLinks: {
    spotify: '',
    appleMusic: '',
    youtube: '',
    instagram: '',
    x: '',
    tiktok: '',
    linkedin: '',
    website: '',
  },
};

const RemixOnboardingContext = createContext<RemixOnboardingContextType | undefined>(undefined);

interface RemixOnboardingProviderProps {
  children: ReactNode;
}

export function RemixOnboardingProvider({ children }: RemixOnboardingProviderProps) {
  const [step, setStep] = useState(1);
  const [data, setData] = useState<RemixOnboardingData>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const router = useRouter();
  // Simple query parameter approach - no complex flow manager needed

  // GraphQL mutation for creating users
  const [createUsers, { loading: mutationLoading }] = useMutation(CREATE_USERS_MUTATION);

  const nextStep = () => {
    if (step < 5) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const goToStep = (targetStep: number) => {
    if (targetStep >= 1 && targetStep <= 5) {
      setStep(targetStep);
    }
  };

  const updateData = (updates: Partial<RemixOnboardingData>) => {
    console.log("🚀 ~ updateData ~ updates:", updates)
    setData(prev => ({
      ...prev,
      ...updates,
      // Handle nested objects properly
      ...(updates.roles && {
        roles: { ...prev.roles, ...updates.roles }
      }),
      ...(updates.socialLinks && {
        socialLinks: { ...prev.socialLinks, ...updates.socialLinks }
      }),
    }));
  };

  const submitOnboarding = async () => {
    setIsLoading(true);
    try {
      // Validate that we have user email from auth context
      if (!user?.email) {
        throw new Error('User email not found. Please ensure you are logged in.');
      }

      // Helper: map key to label
      const keyToLabel: Record<string, string> = {};
      // Import at top of file
      // roleGroups is imported at the top
      (roleGroups as import('./steps/type').RoleGroup[]).forEach((group) => {
        group.roles.forEach((role) => {
          keyToLabel[role.key] = role.label;
        });
      });

      // Build roles array for mutation (no connect wrapper)
      type RoleConnect = { where: { node: { name: { eq: string } } }, edge?: { customRoleName: string } };
      const rolesConnect: RoleConnect[] = [];
      const rolesData = data.roles as typeof data.roles & { otherCustom?: string };
      if ('notInMusic' in rolesData && rolesData.notInMusic) {
        rolesConnect.push({ where: { node: { name: { eq: 'notInMusic' } } } });
      } else {
        Object.entries(rolesData).forEach(([key, value]) => {
          if (key === 'notInMusic' || key === 'otherCustom') return;
          if (key === 'other' && value && 'otherCustom' in rolesData && rolesData.otherCustom && rolesData.otherCustom.trim()) {
            rolesConnect.push({
              where: { node: { name: { eq: 'Other' } } },
              edge: { customRoleName: rolesData.otherCustom.trim() },
            });
          } else if (value) {
            // Use label for backend
            const label = keyToLabel[key] || key;
            rolesConnect.push({ where: { node: { name: { eq: label } } } });
          }
        });
      }

      // Remove unused selectedRoles/primaryRole logic

      // Convert social links to JSON string format (using the exact format from Postman)
      const socialLinksJson = JSON.stringify({
        spotify: data.socialLinks.spotify,
        youtube: data.socialLinks.youtube,
        instagram: data.socialLinks.instagram,
        linkedin: data.socialLinks.linkedin,
        website: data.socialLinks.website,
        appleMusic: data.socialLinks.appleMusic,
      });

      // Prepare mutation input using the exact structure from Postman
      const mutationInput = {
        email: user.email,
        name: data.realName,
        artistName: data.artistName,
        phoneNumber: data.phoneNumber,
        profileImage: data.profilePhotoS3Url || null, // Send S3 key only
        bio: data.bio,
        location: data.location,
        socialLinks: socialLinksJson,
        roles: rolesConnect,
        isOnboarded: true
      };

      console.log('Submitting remix onboarding data:', mutationInput);

      // Execute the mutation
      // Actually call the mutation with the correct profileImage (viewUrl preferred)
      const result = await createUsers({
        variables: mutationInput
      });

      console.log('Mutation result:', result);

      if (result.data?.createUsers?.info?.nodesCreated > 0) {
        toast.success('Onboarding completed successfully!', {
          position: 'top-right',
          className: 'bg-primary/10 text-primary',
        });

        // Synchronize localStorage with GraphQL state for consistency
        syncOnboardingStatus(true);

        // Simple query parameter approach - check for createApplication and redirect accordingly
        const currentUrl = new URL(window.location.href);
        const createApplication = currentUrl.searchParams.get('createApplication') === 'true';
        const queryParam = createApplication ? '?createApplication=true' : '';

        console.log('Onboarding completed, createApplication:', createApplication);

        // Redirect to remix page (with query param if needed)
        router.push(`/attention${queryParam}`);
      } else {
        throw new Error('Failed to create user profile');
      }
    } catch (error) {
      console.error('Error submitting remix onboarding:', error);
      
      toast.error('Failed to complete onboarding. Please try again.', {
        position: 'top-right',
        className: 'bg-destructive/10 text-destructive',
      });
      
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const resetOnboarding = () => {
    setStep(1);
    setData(initialData);
    setIsLoading(false);
  };

  const value: RemixOnboardingContextType = {
    step,
    data,
    isLoading: isLoading || mutationLoading,
    nextStep,
    prevStep,
    goToStep,
    updateData,
    submitOnboarding,
    resetOnboarding,
  };

  return (
    <RemixOnboardingContext.Provider value={value}>
      {children}
    </RemixOnboardingContext.Provider>
  );
}

export function useRemixOnboarding() {
  const context = useContext(RemixOnboardingContext);
  if (context === undefined) {
    throw new Error('useRemixOnboarding must be used within a RemixOnboardingProvider');
  }
  return context;
}

// Validation helpers
export const validateStep1 = (data: RemixOnboardingData): boolean => {
  return data.realName.trim().length > 0 && data.artistName.trim().length > 0;
};

export const validateStep2 = (data: RemixOnboardingData): boolean => {
  console.log("Validating step 2==", data);

  // Profile photo is now required - check both local preview and S3 URL
  return data.profilePhotoUrl.length > 0 || data.profilePhotoS3Url.length > 0;
};

export const validateStep3 = (data: RemixOnboardingData): boolean => {
  const isValidPhoneNumber = (phone: string): boolean => {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    // Check if it's a valid length (10-15 digits for international numbers)
    return cleanPhone.length >= 10 && cleanPhone.length <= 15;
  };

  return (
    data.bio.trim().length > 0 &&
    data.location.trim().length > 0 &&
    data.phoneNumber.trim().length > 0 &&
    isValidPhoneNumber(data.phoneNumber)
  );
};

export const validateStep4 = (data: RemixOnboardingData): boolean => {
  // At least one role should be selected
  return Object.values(data.roles).some(role => role);
};

export const validateStep5 = (data: RemixOnboardingData): boolean => {
  console.log("Validating step 5==", data);
  // Social links are optional, so always return true
  return true;
};

export const validateCurrentStep = (step: number, data: RemixOnboardingData): boolean => {
  switch (step) {
    case 1:
      return validateStep1(data);
    case 2:
      return validateStep2(data);
    case 3:
      return validateStep3(data);
    case 4:
      return validateStep4(data);
    case 5:
      return validateStep5(data);
    default:
      return false;
  }
};

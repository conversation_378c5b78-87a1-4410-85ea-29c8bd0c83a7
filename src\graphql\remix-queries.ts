// Query to get viewUrl for a profile image key
export const GET_PROFILE_PICTURE_URL = gql`
  query GetProfilePictureUrl($key: String!){
    getProfilePictureUrl(key: $key){
      viewUrl
      key
      expiresIn
    }
  }
`;
import { gql } from "@apollo/client";

/**
 * GraphQL Queries for Remix Flow
 * Uses the remix GraphQL endpoint: https://lt7t8xl8ua.execute-api.us-east-2.amazonaws.com/prod/graphql
 */

// Query to get user information by email
export const GET_USERS_QUERY = gql`
  query Users($email: String!) {
    users(where: { email: {eq : $email} }) {
      email
      name
      artistName
      phoneNumber
      profileImage
      bio
      location
      socialLinks
      isOnboarded
      createdAt
      updatedAt
    }
  }
`;

// Mutation to create a new user - using the exact structure that works in Postman
export const CREATE_USERS_MUTATION = gql`
  mutation CreateUsers($email: String!, $name: String!, $artistName: String!, $phoneNumber: String!, $profileImage: String, $bio: String!, $location: String!, $socialLinks: String!, $isOnboarded: Boolean!, $roles: [UserRolesConnectFieldInput!]) {
    createUsers(
      input: {
        email: $email
        name: $name
        artistName: $artistName
        phoneNumber: $phoneNumber
        profileImage: $profileImage
        bio: $bio
        location: $location
        socialLinks: $socialLinks
        roles: { connect: $roles }
        isOnboarded: $isOnboarded
      }
    ) {
      info {
        nodesCreated
        relationshipsCreated
      }
    }
  }
`;

// Mutation to create an application for the remix contest
export const CREATE_APPLICATIONS_MUTATION = gql`
  mutation CreateApplications($personalNote: String!, $userEmail: String!, $mediaTitle: String!, $mediaUrl: String!, $gigId: ID!) {
    createApplications(
      input: {
        perosnalNote: $personalNote
        user: {
          connect: { 
            where: { 
              node: { 
                email: { eq: $userEmail } 
              } 
            } 
          }
        }
        gig: {
          connect: {
            where: { 
              node: { 
                id: { eq: $gigId } 
              } 
            }
          }
        }
        media: {
          create: {
            node: {
              title: $mediaTitle
              url: $mediaUrl
            }
          }
        }
      }
    ) {
      info {
        nodesCreated
        relationshipsCreated
      }
        applications {
            id
            perosnalNote
            createdAt
            updatedAt
            user {
                email
                name
            }
            gig {
                id
                title
            }
        }
    }
  }
`;

// Query to get S3 presigned upload URL for profile photo
export const GET_PROFILE_UPLOAD_URL = gql`
  query GetProfileUploadUrl($fileName: String!, $contentType: String!) {
    getProfileUploadUrl(fileName: $fileName, contentType: $contentType) {
      uploadUrl
      key
      expiresIn
      viewUrl
    }
  }
`;

// Query to get S3 presigned upload URL for audio (stem) file
export const GET_STEM_UPLOAD_URL = gql`
  query GetApplicationMediaUploadUrl($fileName: String!, $contentType: String!, $gigId: ID!) {
    getApplicationMediaUploadUrl(fileName: $fileName, contentType: $contentType, gigId: $gigId) {
      uploadUrl
      key
      expiresIn
      viewUrl
    }
  }
`;

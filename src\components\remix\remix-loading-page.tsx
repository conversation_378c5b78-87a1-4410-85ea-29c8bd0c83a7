'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';

export function RemixLoadingPage() {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Loading Content */}
      <main className="flex-1 overflow-y-auto bg-background relative">
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-10 space-y-6 lg:space-y-8">
          {/* Loading Spinner */}
          <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6">
            <div className="flex items-center justify-center">
              <Loader2 className="w-12 h-12 animate-spin text-primary" />
            </div>
            <div className="text-center space-y-2">
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground font-arvo">
                Loading...
              </h2>
              <p className="text-muted-foreground text-sm lg:text-base font-lato">
                Checking your onboarding status
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 
"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { SignupForm } from "@/components/auth/signup-form"
import { EmailVerificationForm } from "@/components/auth/email-verification-form"
import { useRouter, useSearchParams } from "next/navigation"
import type { SignupResponse, VerificationResponse } from "@/lib/auth-utils"
import Image from "next/image"
import { getBuildConfig } from "@/lib/build-config"
// Using simple query parameter approach instead of complex pending actions

type SignupStep = "signup" | "verification"

export default function SignupPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [currentStep, setCurrentStep] = useState<SignupStep>("signup")
  const [email, setEmail] = useState("")

  // Check for createApplication query parameter
  useEffect(() => {
    const createApplication = searchParams.get('createApplication') === 'true';
    console.log('Signup page loaded, createApplication:', createApplication);
  }, [searchParams]);

  // Note: Auth redirects handled automatically by AuthWrapper

  const handleSignupSuccess = (response: SignupResponse, email: string) => {
    console.log('Signup successful:', response)
    setEmail(email)
    if (response.nextStep?.signUpStep === "CONFIRM_SIGN_UP") {
      setCurrentStep("verification")
    } else {
      // If no verification needed, redirect to 
      // router.push('/')
    }
  }

  const handleSignupError = (error: string) => {
    console.error('Signup error:', error)
  }

  const handleVerificationSuccess = async (response: VerificationResponse) => {
    console.log('Verification successful:', response)
    const config = getBuildConfig();

    // After verification, redirect to login page with query parameter preserved (only for remix mode)
    if (config.mode === 'remix') {
      const createApplication = searchParams.get('createApplication') === 'true';
      const queryParam = createApplication ? '&createApplication=true' : '';

      console.log('Verification success - preserving createApplication:', createApplication);
      router.push(`/login?message=verification-success${queryParam}`)
    } else {
      router.push('/login?message=verification-success')
    }
  }

  const handleVerificationError = (error: string) => {
    console.error('Verification error:', error)
  }

  const handleBackToSignup = () => {
    setCurrentStep("signup")
    setEmail("")
  }

  return (
    <div className="w-full max-w-md space-y-6">
      <Card>
        <CardHeader className="text-center space-y-4">
          <div className=" flex items-center justify-center mb-0">
                            <Image src="/SMASH-(full)-logo.png" width={180} height={180} alt="logo" />
            
                   </div>
          <div>
          {currentStep !== "signup" &&  <CardTitle className="text-2xl">
             Verify Your Email
            </CardTitle>}
            <CardDescription className="mt-2">
              {currentStep === "signup" 
                ? "Create your account to get started" 
                : "Complete your registration"
              }
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {currentStep === "signup" ? (
            <SignupForm
              onSuccess={handleSignupSuccess}
              onError={handleSignupError}
            />
          ) : (
            <EmailVerificationForm
              email={email}
              onSuccess={handleVerificationSuccess}
              onError={handleVerificationError}
              onBack={handleBackToSignup}
            />
          )}
        </CardContent>
      </Card>

      {currentStep === "signup" && (
        <Card className="bg-muted/50">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-center">Why join Smash Music?</h3>
              <div className="grid gap-3 text-sm">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span>Discover new artists and music</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span>Create and share playlists</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span>Connect with your favorite artists</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span>Access exclusive content</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

import { Suspense } from "react";
import PlaylistPage from "@/features/playlist/component/playlist-page";

function PlaylistContent() {
  return <PlaylistPage />;
}

export default function Page() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading playlist...</p>
        </div>
      </div>
    }>
      <PlaylistContent />
    </Suspense>
  );
}

'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { remixThemeConfig, RemixThemeMode } from './theme-config';

interface RemixThemeContextType {
  mode: RemixThemeMode;
}

const RemixThemeContext = createContext<RemixThemeContextType | undefined>(undefined);

export function useRemixTheme() {
  const context = useContext(RemixThemeContext);
  if (context === undefined) {
    throw new Error('useRemixTheme must be used within a RemixThemeProvider');
  }
  return context;
}

interface RemixThemeProviderProps {
  children: React.ReactNode;
}

// Safari-compatible color conversion function
function hslToRgb(h: number, s: number, l: number): string {
  s /= 100;
  l /= 100;
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;
  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return `${r}, ${g}, ${b}`;
}

// Parse HSL string and convert to RGB for Safari compatibility
function parseHslToRgb(hslString: string): string {
  const match = hslString.match(/(\d+(?:\.\d+)?),?\s*(\d+(?:\.\d+)?)%?,?\s*(\d+(?:\.\d+)?)%?/);
  if (!match) return '0, 0, 0';

  const h = parseFloat(match[1]);
  const s = parseFloat(match[2]);
  const l = parseFloat(match[3]);

  return hslToRgb(h, s, l);
}

export function RemixThemeProvider({ children }: RemixThemeProviderProps) {
  // const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    // Force light theme on document root for remix flow
    const root = document.documentElement;
    // const originalClasses = Array.from(root.classList);
    root.classList.remove('dark', 'system');
    root.classList.add('light');

    // Cleanup: restore original theme when component unmounts
    return () => {
      // Get the current theme from localStorage to restore
      const savedTheme = localStorage.getItem('theme') || 'system';
      root.classList.remove('light', 'dark', 'system');

      if (savedTheme === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        root.classList.add(systemTheme);
      } else {
        root.classList.add(savedTheme);
      }
    };
  }, []);

  // Force light theme mode for remix flow
  const mode: RemixThemeMode = 'light';
    // const mode: RemixThemeMode = resolvedTheme === 'dark' ? 'dark' : 'light';

  const themeColors = remixThemeConfig[mode];

  // Generate CSS custom properties for the current theme with Safari compatibility
  const cssVariables = React.useMemo(() => {
    const vars: Record<string, string> = {};

    // Font families for remix pages - use CSS variables from Next.js font imports
    vars['--font-primary'] = `var(--font-zilla-slab), 'Zilla Slab', serif`;
    vars['--font-secondary'] = `var(--font-inter), 'Inter', sans-serif`;
    // Default font-family for the whole remix flow
    vars['--font-family'] = `var(--font-primary)`;

    // Primary colors - provide both HSL and RGB fallbacks for Safari
    const primaryRgb = parseHslToRgb(themeColors.primary.DEFAULT);
    const primaryForegroundRgb = parseHslToRgb(themeColors.primary.foreground);
    vars['--primary'] = `rgb(${primaryRgb})`;
    vars['--primary-hsl'] = `hsl(${themeColors.primary.DEFAULT})`;
    vars['--primary-foreground'] = `rgb(${primaryForegroundRgb})`;
    vars['--primary-foreground-hsl'] = `hsl(${themeColors.primary.foreground})`;

    // Secondary colors
    const secondaryRgb = parseHslToRgb(themeColors.secondary.DEFAULT);
    const secondaryForegroundRgb = parseHslToRgb(themeColors.secondary.foreground);
    vars['--secondary'] = `rgb(${secondaryRgb})`;
    vars['--secondary-hsl'] = `hsl(${themeColors.secondary.DEFAULT})`;
    vars['--secondary-foreground'] = `rgb(${secondaryForegroundRgb})`;
    vars['--secondary-foreground-hsl'] = `hsl(${themeColors.secondary.foreground})`;

    // Muted colors for subtle elements
    const mutedRgb = parseHslToRgb(themeColors.muted.DEFAULT);
    const mutedForegroundRgb = parseHslToRgb(themeColors.muted.foreground);
    vars['--muted'] = `rgb(${mutedRgb})`;
    vars['--muted-hsl'] = `hsl(${themeColors.muted.DEFAULT})`;
    vars['--muted-foreground'] = `rgb(${mutedForegroundRgb})`;
    vars['--muted-foreground-hsl'] = `hsl(${themeColors.muted.foreground})`;

    // Accent colors
    const accentRgb = parseHslToRgb(themeColors.accent.DEFAULT);
    const accentForegroundRgb = parseHslToRgb(themeColors.accent.foreground);
    vars['--accent'] = `rgb(${accentRgb})`;
    vars['--accent-hsl'] = `hsl(${themeColors.accent.DEFAULT})`;
    vars['--accent-foreground'] = `rgb(${accentForegroundRgb})`;
    vars['--accent-foreground-hsl'] = `hsl(${themeColors.accent.foreground})`;

    // Background and foreground
    const backgroundRgb = parseHslToRgb(themeColors.background);
    const foregroundRgb = parseHslToRgb(themeColors.foreground);
    vars['--background'] = `rgb(${backgroundRgb})`;
    vars['--background-hsl'] = `hsl(${themeColors.background})`;
    vars['--foreground'] = `rgb(${foregroundRgb})`;
    vars['--foreground-hsl'] = `hsl(${themeColors.foreground})`;

    // Card colors
    const cardRgb = parseHslToRgb(themeColors.card.DEFAULT);
    const cardForegroundRgb = parseHslToRgb(themeColors.card.foreground);
    vars['--card'] = `rgb(${cardRgb})`;
    vars['--card-hsl'] = `hsl(${themeColors.card.DEFAULT})`;
    vars['--card-foreground'] = `rgb(${cardForegroundRgb})`;
    vars['--card-foreground-hsl'] = `hsl(${themeColors.card.foreground})`;

    // UI elements
    const borderRgb = parseHslToRgb(themeColors.border);
    const inputRgb = parseHslToRgb(themeColors.input);
    const ringRgb = parseHslToRgb(themeColors.ring);
    vars['--border'] = `rgb(${borderRgb})`;
    vars['--border-hsl'] = `hsl(${themeColors.border})`;
    vars['--input'] = `rgb(${inputRgb})`;
    vars['--input-hsl'] = `hsl(${themeColors.input})`;
    vars['--ring'] = `rgb(${ringRgb})`;
    vars['--ring-hsl'] = `hsl(${themeColors.ring})`;

    // Destructive colors
    const destructiveRgb = parseHslToRgb(themeColors.destructive.DEFAULT);
    const destructiveForegroundRgb = parseHslToRgb(themeColors.destructive.foreground);
    vars['--destructive'] = `rgb(${destructiveRgb})`;
    vars['--destructive-hsl'] = `hsl(${themeColors.destructive.DEFAULT})`;
    vars['--destructive-foreground'] = `rgb(${destructiveForegroundRgb})`;
    vars['--destructive-foreground-hsl'] = `hsl(${themeColors.destructive.foreground})`;

    return vars;
  }, [themeColors]);

  // Apply CSS variables to document root for better Safari compatibility
  useEffect(() => {
    if (!mounted) return;

    const root = document.documentElement;

    // Apply all CSS variables to the document root
    Object.entries(cssVariables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });

    // Cleanup function to remove variables when component unmounts
    return () => {
      Object.keys(cssVariables).forEach((key) => {
        root.style.removeProperty(key);
      });
    };
  }, [mounted, cssVariables]);

  if (!mounted) {
    return <div className="opacity-0">{children}</div>;
  }

  return (
    <RemixThemeContext.Provider value={{ mode }}>
      <div
        className="remix-theme-scope min-h-screen"
        style={{ fontFamily: 'var(--font-family)' }}
        data-theme={mode}
      >
        {children}
      </div>
    </RemixThemeContext.Provider>
  );
}

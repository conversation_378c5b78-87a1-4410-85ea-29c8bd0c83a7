'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useRemixOnboarding, validateStep4 } from '../remix-onboarding-context';
import { InitialRoles, RoleGroup, RoleKey } from './type';
import { roleGroups } from './data';
import { getBlankRoles } from './blankRoles';



export default function Step4() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  // Add otherCustom to roles state for custom value
  const [roles, setRoles] = useState<InitialRoles>({ ...getBlankRoles(false), ...data.roles });
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // Sync customOther with roles.otherCustom
  const customOther = roles.otherCustom || '';

  // Helper to clear all roles except notInMusic
  const clearAllRoles = () => {
    const blank = getBlankRoles(false);
    blank.notInMusic = true;
    blank.otherCustom = '';
    return blank;
  };

  // If notInMusic is checked, uncheck all other roles and clear otherCustom
  const handleIsInMusicChange = (checked: boolean) => {
    if (checked) {
      const cleared = clearAllRoles();
      setRoles(cleared);
      updateData({ roles: cleared });
    } else {
      const updatedRoles = { ...roles, notInMusic: false };
      setRoles(updatedRoles);
      updateData({ roles: updatedRoles });
    }
  };

  // If any role is checked, uncheck notInMusic
  const handleRoleChange = (roleKey: RoleKey, checked: boolean) => {
    const updatedRoles = { ...roles, [roleKey]: checked, notInMusic: false };
    // If unchecking 'other', clear custom value
    if (roleKey === 'other' && !checked) {
      updatedRoles.otherCustom = '';
    }
    setRoles(updatedRoles);
    updateData({ roles: updatedRoles });
  };

  const handleGroupSelectAll = (group: RoleGroup, checked: boolean) => {
    const updatedRoles = { ...roles };
    group.roles.forEach(role => {
      // Only assign if the key is a boolean property (not 'otherCustom')
      if (role.key !== 'otherCustom') {
        updatedRoles[role.key as Exclude<RoleKey, 'otherCustom'>] = checked;
      }
    });
    updatedRoles.notInMusic = false;
    setRoles(updatedRoles);
    updateData({ roles: updatedRoles });
  };

  const handleContinue = () => {
    // Add customOther to roles if present
    const updatedRoles = { ...roles };
    if (roles.other && customOther.trim()) {
      updatedRoles.otherCustom = customOther.trim();
    } else {
      updatedRoles.otherCustom = '';
    }
    if (validateStep4({ ...data, roles: updatedRoles })) {
      updateData({ roles: updatedRoles });
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const isValid =
    (roles.notInMusic ||
      Object.entries(roles).some(([key, value]) => key !== 'notInMusic' && key !== 'otherCustom' && value)) &&
    (!roles.other || (roles.other && customOther.trim().length > 0));

  return (
    <div className="space-y-6">
      {/* Title */}
      <h1 className="text-foreground text-3xl font-bold  leading-tight">
        Pick roles that fit you
      </h1>

      {/* Subtitle */}
      <p className="text-sm text-foreground font-secondary">
        Select roles that match your vibe (at least one required)
      </p>

      {/* Role Groups */}
      <Accordion
        type="multiple"
        value={expandedGroups}
        onValueChange={setExpandedGroups}
        className="space-y-4 mb-4"
      >
        {roleGroups.map((group, index) => {
          const selectedCount = group.roles.filter(role => roles[role.key as keyof InitialRoles]).length;
          const allRolesSelected = selectedCount === group.roles.length;
          const someRolesSelected = selectedCount > 0 && !allRolesSelected;


          return (
            <AccordionItem
              key={group.id}
              value={group.id}
              className={`border border-border rounded-lg bg-card/60 overflow-hidden ${index === roleGroups.length - 1 ? "!border-b-1" : ""}`}
            >
              <AccordionTrigger className="w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors [&[data-state=open]>svg]:rotate-180">
                <div className="flex items-center gap-3">
                  <div onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      checked={allRolesSelected ? true : someRolesSelected ? 'indeterminate' : false}
                      onCheckedChange={(checked) => handleGroupSelectAll(group, checked as boolean)}
                      className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                    />
                  </div>
                  <span className="text-card-foreground text-neutral-800 text-base font-semibold leading-normal">
                    {group.title}
                  </span>
                </div>
              </AccordionTrigger>

              <AccordionContent className="px-4 pb-4 space-y-1.5 mb-2">
                {group.roles.map((role) => {
                  if (role.key === 'other') {
                    return (
                      <div key={role.key} className="flex items-center gap-3 py-1">
                        <Checkbox
                          id={role.key}
                          checked={!!roles[role.key as keyof InitialRoles]}
                          onCheckedChange={(checked) => {
                            handleRoleChange(role.key, checked as boolean);
                            if (checked && !customOther) {
                              setTimeout(() => inputRef.current?.focus(), 0);
                            }
                          }}
                          className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                        />
                        <Label
                          htmlFor={role.key}
                          className="text-muted-foreground text-sm font-normal font-secondary leading-[16.86px] cursor-pointer"
                        >
                          {role.label}
                        </Label>
                        <input
                          ref={inputRef}
                          type="text"
                          value={customOther}
                          onChange={e => {
                            const value = e.target.value;
                            const updatedRoles = { ...roles, otherCustom: value };
                            setRoles(updatedRoles);
                            if (!roles.other) {
                              handleRoleChange(role.key, true);
                            } else {
                              updateData({ roles: updatedRoles });
                            }
                          }}
                          disabled={!roles.other}
                          required={roles.other}
                          placeholder="Please specify..."
                          className={`ml-2 px-2 py-1 border font-secondary rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary ${!roles.other ? 'bg-muted cursor-not-allowed' : ''}`}
                        />
                      </div>
                    );
                  }

                  return (
                    <div key={role.key} className="flex items-center gap-3 py-1">
                      <Checkbox
                        id={role.key}
                        checked={!!roles[role.key as keyof InitialRoles]}
                        onCheckedChange={(checked) => handleRoleChange(role.key, checked as boolean)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && isValid) {
                            handleContinue();
                          }
                        }}
                        className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Label
                        htmlFor={role.key}
                        className="text-muted-foreground text-sm font-normal font-secondary leading-[16.86px] cursor-pointer"
                      >
                        {role.label}
                      </Label>
                    </div>
                  );
                })}
                {/* Show custom selected label if present */}
                {roles.other && customOther && (
                  <div className="flex items-center gap-2 mt-1 ml-8 text-xs text-primary">
                    Custom: {customOther}
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
      <div
        className={
          'border border-border rounded-lg bg-card/60 overflow-hidden flex items-center gap-3 p-4 ' +
          ' ' +
          (roles.notInMusic ? 'ring-2 ring-primary' : '')
        }
      >
        <Checkbox
          checked={roles.notInMusic}
          onCheckedChange={handleIsInMusicChange}
          className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
        />
        <span className="text-base font-semibold text-card-foreground text-neutral-800 leading-normal">
          I do not work in music
        </span>
      </div>
      {/* Show warning below the 'I do not work in music' box if needed */}
      {(() => {
        // Find if any group has the 'other' role
        const hasOtherRole = roleGroups.some(group => group.roles.some(role => role.key === 'other'));
        const showOtherWarning = hasOtherRole && roles.other && !customOther.trim();
        return showOtherWarning ? (
          <span className="ml-3 text-xs text-red-600 font-base font-secondary">Please add custom role in others section</span>
        ) : null;
      })()}

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="cursor-pointer px-6 py-3  font-bold text-base border-muted text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="cursor-pointer  px-6 py-3 bg-primary text-primary-foreground  font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}

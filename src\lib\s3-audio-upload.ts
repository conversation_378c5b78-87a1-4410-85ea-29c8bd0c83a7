/**
 * S3 Audio Upload Utilities
 * Handles audio validation, pre-signed URL fetching, and S3 upload functionality
 * 
 * Supported formats: AIF, AIFF, WAV, MP3, MP4, AAC
 * Max file size: 100MB
 * 
 * Usage:
 * 1. Import uploadAudioToS3 function
 * 2. Call with file and progress callback
 * 3. Handle success/error response
 */

import type {
  AudioUploadConfig,
  AudioValidationResult,
  AudioUploadResult,
  AudioUploadOptions,
  S3PreSignedUrlRequest,
  S3PreSignedUrlResponse,
  S3PreSignedUrlBody,
  UploadProgressCallback,
} from '@/types/s3-upload';

import {
  DEFAULT_AUDIO_UPLOAD_CONFIG,
  S3_UPLOAD_API_CONFIG,
} from '@/types/s3-upload';

/**
 * Validate an audio file against upload requirements
 */
export function validateAudioFile(
  file: File,
  config: AudioUploadConfig = DEFAULT_AUDIO_UPLOAD_CONFIG
): Promise<AudioValidationResult> {
  return new Promise((resolve) => {
    const warnings: string[] = [];

    // Check file type
    const isValidType = config.allowedTypes.includes(file.type) ||
      config.allowedExtensions.some(ext => 
        file.name.toLowerCase().endsWith(ext.toLowerCase())
      );

    if (!isValidType) {
      return resolve({
        isValid: false,
        error: `Please select a supported audio file. We support: AIF, AIFF, WAV, MP3, MP4, and AAC formats only.`,
      });
    }

    // Check file size
    if (file.size > config.maxSizeBytes) {
      const maxSizeMB = Math.round(config.maxSizeBytes / (1024 * 1024));
      return resolve({
        isValid: false,
        error: `Audio file size must be less than ${maxSizeMB}MB. Current size: ${formatFileSize(file.size)}`,
      });
    }

    // Check file name
    if (!file.name || file.name.trim().length === 0) {
      return resolve({
        isValid: false,
        error: 'Audio file must have a valid name',
      });
    }

    // For audio files, we don't need to check dimensions like images
    // We can optionally check audio duration if needed in the future
    resolve({
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined,
    });
  });
}

/**
 * Generate a unique filename for S3 upload
 */
export function generateS3AudioFilename(originalFile: File): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const extension = getFileExtension(originalFile.name);
  
  return `${S3_UPLOAD_API_CONFIG.filePathPrefix}audio_${timestamp}_${randomSuffix}.${extension}`;
}

/**
 * Fetch pre-signed URL from API
 */
export async function fetchAudioPreSignedUrl(
  filename: string
): Promise<S3PreSignedUrlBody> {
  const request: S3PreSignedUrlRequest = { filename };

  const response = await fetch(S3_UPLOAD_API_CONFIG.endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  const data: S3PreSignedUrlResponse = await response.json();

  if (data.statusCode !== 200 || !data.body) {
    throw new Error('Invalid API response: missing response body');
  }

  // Parse the JSON string in the body
  let parsedBody: S3PreSignedUrlBody;
  try {
    parsedBody = JSON.parse(data.body);
  } catch {
    throw new Error('Invalid API response: body is not valid JSON');
  }

  if (!parsedBody.uploadUrl || !parsedBody.viewUrl) {
    throw new Error('Invalid API response: missing upload URL or view URL');
  }

  return parsedBody;
}

/**
 * Upload audio file to S3 using pre-signed URL
 */
export async function uploadAudioFileToS3(
  file: File,
  uploadUrl: string,
  onProgress?: UploadProgressCallback
): Promise<void> {
  const xhr = new XMLHttpRequest();

  return new Promise<void>((resolve, reject) => {
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress = Math.round((event.loaded / event.total) * 100);
        onProgress(progress);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        resolve();
      } else {
        reject(new Error(`S3 upload failed: ${xhr.status} ${xhr.statusText}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('S3 upload failed: Network error'));
    });

    xhr.addEventListener('timeout', () => {
      reject(new Error('S3 upload failed: Request timeout'));
    });

    xhr.open('PUT', uploadUrl);
    xhr.setRequestHeader('Content-Type', file.type);
    xhr.timeout = 300000; // 5 minute timeout for larger audio files
    xhr.send(file);
  });
}

/**
 * Complete audio upload process (validation + pre-signed URL + S3 upload)
 */
export async function uploadAudioToS3(
  file: File,
  options: AudioUploadOptions = {}
): Promise<AudioUploadResult> {
  const { onProgress } = options;

  try {
    // Step 1: Validate audio
    onProgress?.(5);
    const validation = await validateAudioFile(file);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    // Step 2: Generate filename and get pre-signed URL
    onProgress?.(15);
    const filename = generateS3AudioFilename(file);
    const preSignedResponse = await fetchAudioPreSignedUrl(filename);

    // Step 3: Upload to S3
    onProgress?.(25);
    await uploadAudioFileToS3(
      file,
      preSignedResponse.uploadUrl,
      (progress) => {
        // Map S3 upload progress (25-100%)
        const mappedProgress = 25 + (progress * 0.75);
        onProgress?.(Math.round(mappedProgress));
      }
    );

    // Step 4: Use the viewUrl for displaying the audio
    return {
      success: true,
      url: preSignedResponse.viewUrl, // Use viewUrl for accessing the audio
      objectKey: preSignedResponse.objectKey,
    };
  } catch (error) {
    console.error('Audio upload failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed. Please try again.',
    };
  }
}

/**
 * Utility function to format file size
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file extension from filename
 */
function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
} 